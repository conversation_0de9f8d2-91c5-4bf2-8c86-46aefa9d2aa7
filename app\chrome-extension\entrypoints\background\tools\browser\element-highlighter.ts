import { createErrorResponse, ToolResult } from '@/common/tool-handler';
import { BaseBrowserToolExecutor } from '../base-browser';
import { TOOL_NAMES } from 'chrome-mcp-shared';
import { TOOL_MESSAGE_TYPES } from '@/common/message-types';
import { ERROR_MESSAGES } from '@/common/constants';

interface ElementHighlighterToolParams {
  enable?: boolean; // 启用或禁用元素高亮显示 (default: true)
  elementTypes?: string[]; // 指定要高亮的元素类型数组 (默认所有类型)
  colorScheme?: string; // 颜色方案选择: "default" | "rainbow" | "monochrome"
}

/**
 * Tool for highlighting HTML elements on web pages with colored borders
 * 为网页HTML元素添加彩色边框高亮显示的工具
 */
class ElementHighlighterTool extends BaseBrowserToolExecutor {
  name = TOOL_NAMES.BROWSER.HIGHLIGHT_PAGE_ELEMENTS;

  /**
   * Execute element highlighting operation
   */
  async execute(args: ElementHighlighterToolParams): Promise<ToolResult> {
    const {
      enable = true,
      elementTypes = [], // 空数组表示所有类型
      colorScheme = 'default',
    } = args;

    console.log(`Starting element highlighter with options:`, args);

    try {
      // Get current tab
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (!tabs[0]) {
        return createErrorResponse(ERROR_MESSAGES.TAB_NOT_FOUND);
      }

      const tab = tabs[0];
      if (!tab.id) {
        return createErrorResponse(ERROR_MESSAGES.TAB_NOT_FOUND + ': Active tab has no ID');
      }

      // Inject the element highlighter helper script
      await this.injectContentScript(tab.id, ['inject-scripts/element-highlighter-helper.js']);

      // Send message to content script
      const action = enable ? TOOL_MESSAGE_TYPES.HIGHLIGHT_PAGE_ELEMENTS : TOOL_MESSAGE_TYPES.REMOVE_PAGE_HIGHLIGHTS;
      
      const result = await this.sendMessageToTab(tab.id, {
        action,
        enable,
        elementTypes,
        colorScheme,
      });

      if (result.error) {
        return createErrorResponse(result.error);
      }

      return {
        content: [
          {
            type: 'text',
            text: JSON.stringify({
              success: true,
              message: enable 
                ? '页面元素高亮显示已启用 / Page element highlighting enabled'
                : '页面元素高亮显示已禁用 / Page element highlighting disabled',
              elementsHighlighted: result.elementsHighlighted || 0,
              colorScheme: colorScheme,
              elementTypes: elementTypes.length > 0 ? elementTypes : 'all',
              url: tab.url,
              title: tab.title,
            }),
          },
        ],
        isError: false,
      };
    } catch (error) {
      console.error('Error in element highlighter operation:', error);
      return createErrorResponse(
        `Error highlighting page elements: ${error instanceof Error ? error.message : String(error)}`,
      );
    }
  }
}

export const elementHighlighterTool = new ElementHighlighterTool();