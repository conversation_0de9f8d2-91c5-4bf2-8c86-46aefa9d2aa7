# Chrome MCP新功能开发详细步骤指南

基于"页面元素高亮功能"成功实现的完整开发流程思维导图

## 🎯 总体流程概览

```
新功能开发流程
├── 1. 需求分析和设计阶段
├── 2. 工具定义阶段  
├── 3. 代码实现阶段
├── 4. 配置和注册阶段
├── 5. 构建和部署阶段
├── 6. 测试和验证阶段
└── 7. 问题排查和解决阶段
```

---

## 1. 需求分析和设计阶段 🔍

### 1.1 功能需求分析
- **目标**: 明确功能具体要求和用户期望
- **步骤**: 
  - 分析用户需求描述
  - 确定功能边界和范围
  - 定义输入输出参数
- **作用**: 避免开发过程中需求变更，确保功能符合预期
- **输出**: 功能需求文档和设计方案

### 1.2 技术架构设计
- **目标**: 确定实现技术路径和架构模式
- **步骤**:
  - 选择合适的Chrome API
  - 设计MCP工具接口
  - 规划代码结构和文件组织
- **作用**: 确保技术可行性，避免后期架构调整
- **输出**: 技术方案和文件结构设计

---

## 2. 工具定义阶段 📋

```
工具定义阶段
├── 2.1 MCP工具Schema定义
├── 2.2 消息类型定义
└── 2.3 接口类型定义
```

### 2.1 MCP工具Schema定义
- **文件**: `packages/shared/src/tools.ts`
- **步骤**:
  ```typescript
  // 添加工具名称常量
  HIGHLIGHT_PAGE_ELEMENTS: 'chrome_highlight_page_elements'
  
  // 定义完整inputSchema
  {
    type: "object",
    properties: {
      enable: { type: "boolean" },
      elementTypes: { type: "array", items: { type: "string" } },
      colorScheme: { type: "string", enum: ["default", "rainbow", "monochrome"] }
    }
  }
  ```
- **作用**: 
  - 定义MCP协议标准接口
  - 确保参数验证和类型安全
  - 提供工具描述和使用说明
- **原因**: MCP服务器根据schema验证请求参数，缺少会导致工具无法识别

### 2.2 消息类型定义
- **文件**: `app/chrome-extension/common/message-types.ts`
- **步骤**:
  ```typescript
  HIGHLIGHT_PAGE_ELEMENTS: 'highlightPageElements',
  REMOVE_PAGE_HIGHLIGHTS: 'removePageHighlights'
  ```
- **作用**: 统一Chrome扩展内部消息通信标准
- **原因**: background script与content script通信需要标准化消息类型

### 2.3 接口类型定义（可选）
- **文件**: `packages/shared/src/types.ts`
- **步骤**: 定义TypeScript接口类型
- **作用**: 提供类型安全和IDE智能提示
- **原因**: 确保开发时参数类型正确性

---

## 3. 代码实现阶段 💻

```
代码实现阶段
├── 3.1 工具执行器实现
├── 3.2 内容脚本开发
├── 3.3 核心逻辑实现
└── 3.4 工具导出注册
```

### 3.1 工具执行器实现
- **文件**: `app/chrome-extension/entrypoints/background/tools/browser/element-highlighter.ts`
- **步骤**:
  ```typescript
  class ElementHighlighterTool extends BaseBrowserToolExecutor {
    async execute(params: any): Promise<ToolResult> {
      // 参数处理和验证
      // 调用Chrome API执行content script
      // 返回执行结果
    }
  }
  ```
- **作用**:
  - 处理MCP请求参数
  - 调用Chrome扩展API
  - 统一错误处理和响应格式
- **原因**: MCP工具的标准实现模式，确保与系统集成

### 3.2 内容脚本开发
- **文件**: `app/chrome-extension/inject-scripts/element-highlighter-helper.js`
- **步骤**:
  1. 定义元素配置系统
     ```javascript
     const ELEMENT_HIGHLIGHT_CONFIG = {
       'div': { priority: 'low', color: '#4CAF50', category: 'layout' },
       'button': { priority: 'high', color: '#FF9800', category: 'interactive' }
       // ... 30+种元素类型
     }
     ```
  2. 实现颜色方案系统
  3. 开发CSS样式注入机制
  4. 创建元素过滤和可见性检测
- **作用**:
  - 实际DOM操作和样式修改
  - 提供丰富的视觉反馈
  - 支持多种配置选项
- **原因**: 网页元素操作必须在content script环境中执行

### 3.3 核心逻辑实现
- **关键算法**:
  - 元素发现: `document.querySelectorAll('*')`
  - 可见性检测: `getComputedStyle` + `getBoundingClientRect`
  - 样式注入: 动态创建`<style>`标签
  - 优先级排序: 基于元素类型和重要性
- **作用**: 确保功能的核心价值实现
- **原因**: 复杂的DOM操作需要精心设计的算法

### 3.4 工具导出注册
- **文件**: `app/chrome-extension/entrypoints/background/tools/browser/index.ts`
- **步骤**:
  ```typescript
  export { elementHighlighterTool } from './element-highlighter'
  ```
- **作用**: 使新工具对系统可见
- **原因**: ES模块系统需要显式导出

---

## 4. 配置和注册阶段 ⚙️

```
配置和注册阶段
├── 4.1 Chrome扩展Key配置 ⚠️ CRITICAL
├── 4.2 MCP服务器配置
└── 4.3 Native Messaging配置
```

### 4.1 Chrome扩展Key配置 ⚠️ **最关键步骤**
- **文件**: `app/chrome-extension/.env`
- **步骤**:
  ```env
  CHROME_EXTENSION_KEY="-----BEGIN PRIVATE KEY-----
  MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQDL..."
  -----END PRIVATE KEY-----"
  ```
- **作用**: 
  - 确保扩展ID在构建间保持稳定
  - 维持native messaging连接
  - 避免重新注册和配置
- **原因**: 
  - 没有固定key，每次构建会生成新的扩展ID
  - Native messaging host注册基于特定扩展ID
  - ID变化会破坏MCP通信链路
- **⚠️ 关键性**: 这是最容易遗漏但最关键的步骤

### 4.2 MCP服务器配置
- **全局配置**: `~/.claude/settings.json`
- **项目配置**: `.mcp.json`
- **步骤**: 确保两个配置文件都存在且正确
- **作用**: 让Claude Code识别和连接MCP服务器
- **原因**: Claude需要项目级配置才能激活MCP功能

### 4.3 Native Messaging配置
- **步骤**: 运行 `node dist/cli.js register`
- **作用**: 在系统中注册native messaging host
- **原因**: Chrome扩展与本地程序通信的安全机制

---

## 5. 构建和部署阶段 🔨

```
构建和部署阶段
├── 5.1 项目构建
├── 5.2 Chrome扩展加载
└── 5.3 服务验证
```

### 5.1 项目构建
- **命令**: `pnpm build`
- **步骤**:
  1. 构建shared包 (`packages/shared/`)
  2. 构建Chrome扩展 (`app/chrome-extension/.output/chrome-mv3/`)
  3. 构建native server (`app/native-server/dist/`)
- **作用**: 生成可运行的产物
- **原因**: TypeScript需要编译为JavaScript，资源需要打包

### 5.2 Chrome扩展加载
- **步骤**:
  1. 打开 `chrome://extensions/`
  2. 启用"开发者模式"
  3. 点击"加载已解压的扩展程序"
  4. 选择 `app/chrome-extension/.output/chrome-mv3/` 目录
- **作用**: 将新构建的扩展安装到Chrome中
- **原因**: 开发版本需要手动加载，无法从应用商店安装

### 5.3 服务验证
- **步骤**: 检查扩展popup显示"服务运行中"
- **作用**: 确认所有组件正常启动和连接
- **原因**: 早期发现配置问题，避免后续调试困难

---

## 6. 测试和验证阶段 ✅

```
测试和验证阶段
├── 6.1 基础连接测试
├── 6.2 功能完整性测试
├── 6.3 边界条件测试
└── 6.4 性能和稳定性测试
```

### 6.1 基础连接测试
- **步骤**: 调用 `mcp__chrome-mcp-stdio__get_windows_and_tabs`
- **目的**: 验证MCP通信链路正常
- **作用**: 确保基础设施工作，避免功能问题与连接问题混淆
- **原因**: 先验证基础，再测试具体功能

### 6.2 功能完整性测试
- **步骤**:
  1. 测试基础功能: `chrome_highlight_page_elements(enable=true)`
  2. 测试参数变体: 不同颜色方案和元素类型
  3. 测试开关控制: enable/disable功能
- **作用**: 验证所有设计功能都正确实现
- **原因**: 确保用户体验符合预期

### 6.3 边界条件测试
- **步骤**: 测试空页面、大量元素页面、特殊网站
- **作用**: 验证功能在各种环境下的稳定性
- **原因**: 真实使用环境复杂多样

### 6.4 性能和稳定性测试
- **步骤**: 长时间运行、频繁调用、内存使用监控
- **作用**: 确保功能不影响浏览器性能
- **原因**: 避免用户体验问题和系统资源消耗

---

## 7. 问题排查和解决阶段 🔧

```
问题排查和解决阶段
├── 7.1 常见问题识别
├── 7.2 系统性排查方法
└── 7.3 解决方案记录
```

### 7.1 常见问题识别
- **MCP工具不被识别**:
  - 原因: shared包未更新到全局位置
  - 解决: 复制或使用全局MCP服务器
- **Chrome扩展"服务未启动"**:
  - 原因: 缺少Chrome扩展key
  - 解决: 添加.env文件配置key
- **MCP连接"Not connected"**:
  - 原因: 配置冲突或服务器启动失败
  - 解决: 使用混合架构或重启服务

### 7.2 系统性排查方法
1. **分层验证**: 从MCP连接到Chrome扩展逐层检查
2. **日志分析**: 查看MCP服务器和Chrome扩展日志
3. **配置检查**: 验证所有配置文件的正确性
4. **进程监控**: 确认所有必要服务都在运行

### 7.3 解决方案记录
- **混合架构**: 本地Chrome扩展 + 全局MCP服务器
- **配置原则**: 项目级.mcp.json必须存在
- **构建要求**: 必须包含Chrome扩展key
- **重启策略**: 配置更改后完全重启Claude Code CLI

---

## 🎯 成功架构总结

### 最终稳定架构 ✅
```
成功架构
├── Chrome扩展 (本地构建版本)
│   ├── 包含新功能代码
│   ├── 包含稳定Chrome扩展key
│   └── 手动加载到Chrome浏览器
└── MCP服务器 (全局npm包)
    ├── 使用稳定的全局安装版本
    ├── 通过global settings.json配置
    ├── 通过项目.mcp.json配置
    └── 提供可靠的MCP协议通信
```

### 关键成功要素 🔑
1. **Chrome扩展key**: 确保扩展ID稳定性
2. **混合架构**: 本地扩展 + 全局MCP服务器最可靠
3. **项目级配置**: .mcp.json文件必须存在
4. **全局同步**: shared包更新需要全局可见
5. **完整重启**: 配置更改后必须重启Claude Code CLI

### 开发时间估算 ⏱️
- **简单功能** (如开关控制): 2-4小时
- **中等复杂功能** (如元素高亮): 4-8小时  
- **复杂功能** (如AI分析): 8-16小时
- **调试和测试**: 功能开发时间的50%

---

## 📚 参考资源

### 重要文件位置
- **工具定义**: `packages/shared/src/tools.ts`
- **消息类型**: `app/chrome-extension/common/message-types.ts`
- **工具实现**: `app/chrome-extension/entrypoints/background/tools/browser/`
- **内容脚本**: `app/chrome-extension/inject-scripts/`
- **Chrome Key**: `app/chrome-extension/.env`
- **MCP配置**: `~/.claude/settings.json` + `.mcp.json`

### 调试工具
- Chrome开发者工具 (扩展调试)
- Claude Code CLI日志
- MCP服务器进程监控
- Native messaging host注册状态

### 测试环境
- 不同类型网页 (静态/动态/SPA)
- 不同Chrome版本
- 不同系统环境 (Windows/Linux/Mac)

---

**备注**: 本指南基于"页面元素高亮功能"的实际开发经验总结，已验证可行性和完整性。遵循此流程可以有效避免常见陷阱，提高开发效率和成功率。