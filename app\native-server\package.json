{"name": "mcp-chrome-bridge", "version": "1.0.29", "description": "Chrome Native-Messaging host (Node)", "main": "dist/index.js", "bin": {"mcp-chrome-bridge": "./dist/cli.js", "mcp-chrome-stdio": "./dist/mcp/mcp-server-stdio.js"}, "scripts": {"dev": "nodemon --watch src --ext ts,js,json --ignore dist/ --exec \"npm run build && npm run register:dev\"", "build": "ts-node src/scripts/build.ts", "test": "jest", "test:watch": "jest --watch", "lint": "eslint 'src/**/*.{js,ts}'", "lint:fix": "eslint 'src/**/*.{js,ts}' --fix", "format": "prettier --write 'src/**/*.{js,ts,json}'", "register:dev": "node dist/scripts/register-dev.js", "postinstall": "node dist/scripts/postinstall.js"}, "files": ["dist"], "engines": {"node": ">=14.0.0"}, "preferGlobal": true, "keywords": ["mcp", "chrome", "browser"], "author": "hangye", "license": "MIT", "dependencies": {"@fastify/cors": "^11.0.1", "@modelcontextprotocol/sdk": "^1.11.0", "chalk": "^5.4.1", "chrome-mcp-shared": "workspace:*", "commander": "^13.1.0", "fastify": "^5.3.2", "is-admin": "^4.0.0", "pino": "^9.6.0", "uuid": "^11.1.0"}, "devDependencies": {"@jest/globals": "^29.7.0", "@types/chrome": "^0.0.318", "@types/jest": "^29.5.14", "@types/node": "^22.15.3", "@types/supertest": "^6.0.3", "@typescript-eslint/parser": "^8.31.1", "cross-env": "^7.0.3", "husky": "^9.1.7", "jest": "^29.7.0", "lint-staged": "^15.5.1", "nodemon": "^3.1.10", "pino-pretty": "^13.0.0", "rimraf": "^6.0.1", "supertest": "^7.1.0", "ts-jest": "^29.3.2", "ts-node": "^10.9.2"}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"*.{js,ts}": ["eslint --fix", "prettier --write"], "*.{json,md}": ["prettier --write"]}}