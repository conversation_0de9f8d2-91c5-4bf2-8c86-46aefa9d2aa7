{"mcpServers": {"mcp-feedback-collector": {"type": "stdio", "command": "/mnt/d/Programs/Python/Python311/python.exe", "args": ["-m", "mcp_feedback_collector.server"], "env": {"PYTHONIOENCODING": "utf-8", "MCP_DIALOG_TIMEOUT": "1200", "PYTHONPATH": "/mnt/d/Programs/Python/Python311/Lib/site-packages"}}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {}}, "chrome-mcp-stdio": {"type": "stdio", "command": "node", "args": ["/mnt/c/Users/<USER>/AppData/Roaming/npm/node_modules/mcp-chrome-bridge/dist/mcp/mcp-server-stdio.js"], "env": {"MCP_SERVER_URL": "http://************:12306/mcp", "MCP_HOST": "************", "MCP_PORT": "12306"}}}}