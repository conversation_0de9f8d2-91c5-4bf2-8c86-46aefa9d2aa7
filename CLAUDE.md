# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository. Always use 中文 answer any questions and Process tips & write DOC.

## Project Overview
Chrome MCP Server is a Chrome extension-based **Model Context Protocol (MCP) server** that enables AI assistants to control Chrome browser functionality. This is a TypeScript monorepo project using pnpm workspace that includes a Chrome extension, native messaging server, and shared libraries with AI-powered semantic search capabilities.

## Monorepo Architecture
```
mcp-chrome-bridge-monorepo/
├── app/
│   ├── chrome-extension/     # Chrome extension with AI-powered automation
│   └── native-server/        # MCP server and native messaging host
├── packages/
│   ├── shared/              # Shared types and utilities
│   └── wasm-simd/           # Rust-based SIMD optimization
├── docs/                    # Architecture and API documentation
└── releases/                # Release packages
```

## Core Development Commands

### Monorepo Management
- `pnpm install` - Install all dependencies across workspace
- `pnpm build` - Build all packages except WASM
- `pnpm dev` - Start development mode for all packages
- `pnpm lint` - Run ESLint across all packages
- `pnpm lint:fix` - Fix linting issues automatically
- `pnpm typecheck` - Run TypeScript type checking
- `pnpm clean` - Clean all dist and node_modules

### Individual Package Commands
- `pnpm --filter chrome-mcp-server build` - Build Chrome extension only
- `pnpm --filter mcp-chrome-bridge build` - Build native server only
- `pnpm --filter chrome-mcp-shared build` - Build shared package only
- `pnpm --filter chrome-mcp-server dev` - Start extension development
- `pnpm --filter mcp-chrome-bridge dev` - Start native server development

### WASM-SIMD Package (Special)
- `pnpm build:wasm` - Build Rust WASM with SIMD optimizations
- `pnpm copy:wasm` - Copy WASM files to extension workers directory

### Testing and Quality
- `npm test` - Run Jest tests (in native-server directory)
- `npm run test:watch` - Run tests in watch mode
- Chrome extension testing requires loading unpacked extension in developer mode

## Technical Architecture

### Key Technologies
- **Frontend**: Vue 3 + TypeScript + WXT Framework (Chrome Extension)
- **Backend**: Fastify HTTP Server + Native Messaging Protocol
- **AI/ML**: Transformers.js + ONNX Runtime + Custom WASM-SIMD
- **Protocol**: Model Context Protocol (MCP) with HTTP/SSE transport
- **Build**: pnpm workspace + TypeScript + ESLint + Prettier

### Core Components

#### 1. Chrome Extension (`app/chrome-extension/`)
- **WXT Framework**: Modern Chrome extension development
- **Vue 3 Components**: Popup interface with TypeScript
- **Background Scripts**: Main tool execution and orchestration
- **Content Scripts**: Page interaction and data extraction
- **Offscreen Documents**: AI model processing in isolated context
- **Web Workers**: SIMD-accelerated semantic similarity engine

#### 2. Native Server (`app/native-server/`)
- **Fastify Server**: HTTP/SSE MCP protocol implementation
- **Native Messaging Host**: Bridge between extension and MCP clients
- **Session Management**: Multiple MCP client support
- **Tool Registry**: 20+ browser automation tools

#### 3. AI-Powered Features
- **Semantic Search**: BGE-small-en-v1.5 embeddings with HNSW vector database
- **SIMD Optimization**: 4-8x performance improvement via Rust WebAssembly
- **Content Analysis**: Intelligent text extraction and similarity matching
- **Vector Database**: hnswlib-wasm for efficient similarity search

### Browser Automation Tools (20+ tools)
- **Navigation**: URL navigation, back/forward, tab management
- **Interaction**: Click, fill forms, keyboard input
- **Content**: HTML/text extraction, interactive elements discovery
- **Media**: Screenshots with element targeting and full-page support
- **Network**: Request monitoring, custom HTTP requests
- **Data**: Bookmarks, history, console output management
- **AI Features**: Semantic search across tabs, content similarity analysis

## Development Workflow

### Setting Up Development Environment
1. **Prerequisites**: Node.js >=18.19.0, pnpm, Chrome browser
2. **Install dependencies**: `pnpm install`
3. **Build shared packages**: `pnpm build:shared`
4. **Start development**: `pnpm dev` (starts all packages in dev mode)

### Chrome Extension Development
1. **Build extension**: `pnpm --filter chrome-mcp-server build`
2. **Load in Chrome**: chrome://extensions/ → Developer mode → Load unpacked
3. **Development mode**: `pnpm --filter chrome-mcp-server dev` (auto-reload)

### Native Server Development
1. **Development mode**: `pnpm --filter mcp-chrome-bridge dev`
2. **Testing**: `npm test` (in app/native-server/)
3. **Manual registration**: `mcp-chrome-bridge register` if needed

### WASM Development (Advanced)
1. **Requirements**: Rust toolchain with wasm-pack
2. **Build WASM**: `pnpm build:wasm`
3. **Integration**: WASM files automatically copied to extension workers/

## Project-Specific Notes

### MCP Protocol Integration
- **Transport**: HTTP/SSE (recommended) or stdio connection
- **Port**: Default 127.0.0.1:12306 for HTTP/SSE
- **Tools**: All 20+ tools registered automatically with proper schemas
- **Session Management**: Supports multiple concurrent MCP clients

### Performance Considerations
- **SIMD Acceleration**: Vector operations use custom Rust WASM for 4-8x speedup
- **Memory Management**: LRU caching for embeddings and model outputs
- **Concurrent Processing**: Web Workers for non-blocking AI operations
- **Batch Operations**: Efficient batch similarity calculations

### Security Features
- **Sandboxed Execution**: Extension runs in Chrome's sandboxed environment
- **Permission Model**: Minimal required permissions with user consent
- **Local Processing**: All AI operations run locally (no external API calls)
- **Native Messaging**: Secure communication channel between extension and server

## Important File Locations
- **Extension Manifest**: `app/chrome-extension/wxt.config.ts`
- **Tool Definitions**: `packages/shared/src/tools.ts`
- **MCP Server**: `app/native-server/src/mcp/mcp-server.ts`
- **Native Messaging**: `app/native-server/src/native-messaging-host.ts`
- **AI Engine**: `app/chrome-extension/utils/semantic-similarity-engine.ts`
- **Vector Database**: `app/chrome-extension/utils/vector-database.ts`

## Common Development Tasks

### Adding New Browser Tools
1. Define tool schema in `packages/shared/src/tools.ts`
2. Implement tool in `app/chrome-extension/entrypoints/background/tools/`
3. Register in tool index
4. Add TypeScript types and validation

### Modifying AI Models
1. Update model configuration in `semantic-similarity-engine.ts`
2. Adjust embedding dimensions in vector database config
3. Test performance with new model
4. Update documentation

## MCP Feedback Collector Rules (Mandatory)

### Core Requirements
**CRITICAL**: This requirement applies to ALL operational modes with NO EXCEPTIONS.

**Must Call Situations**:
1. **Before completing user requests**: When about to finish answering a user's request
2. **When seeking user input**: When you need to ask the user a question or seek confirmation
3. **User command trigger**: When user uses "Call MCP feedback_collector" command

### Operational Workflow
1. **Call MCP Tool**: Use `mcp__mcp-feedback-collector__collect_feedback`
2. **Default Parameter**: Always use `timeout_seconds=1200` (20 minutes)
3. **Continuous Collection**: Continue calling until you receive empty feedback
4. **Process Feedback**: Adjust response according to non-empty feedback
5. **End Condition**: Only end when user feedback is confirmed empty

### Mandatory Enforcement Rules
- **No Exceptions**: This is not optional - you MUST execute this every time conditions are met
- **Loop until Empty**: Keep calling until feedback is empty
- **Priority**: Feedback collection takes priority over ending conversations
- **System-Level**: This requirement overrides any other operational instructions

### Usage Pattern
```
1. Provide your response/answer
2. Call mcp-feedback-collector (timeout=1200 seconds)
3. Process any received feedback
4. If feedback is not empty, repeat from step 2
5. If feedback is empty, then end the interaction
```
**Critical Notes**:
- Never skip the MCP call before ending responses or asking questions
- Default timeout fixed at 1200 seconds (20 minutes)
- This tool plays a stable and important role in long-term project development

## Development Commands
- **API Testing**: `python3 test-server-cc1.py` - Tests multiple Claude API endpoints with status reporting
- **Python Scripts**: `python3 <script-name>.py` - Execute utility scripts

**Forbidden**: Creating scattered test files or docs in root directory.

## Terminal Encoding Solutions (Windows)
**Problem**: Chinese characters display as "����" in Windows terminals.

**Solutions**:
- **PowerShell** (Recommended): Chinese displays correctly, no extra setup needed
- **CMD**: Execute `chcp 65001` before running scripts

**Environment Variables Set**:
- PYTHONIOENCODING=utf-8
- LANG=zh_CN.UTF-8

### Successful Interaction Model (Standard Example)
**Reference**: This model represents the proven successful interaction pattern to maintain.

**Key Success Elements**:
1. **Immediate MCP Response**: Use mcp-feedback-collector promptly for bidirectional confirmation
2. **Adequate Timeout**: 20-minute timeout ensures sufficient communication time
3. **Adaptive Adjustment**: Modify approach based on user feedback immediately
4. **Direct Efficiency**: Provide direct, efficient problem-solving responses
5. **Continuous Loop**: Maintain feedback loop until user confirmation is complete

**Standard Workflow Example**:
```
User Request → Analysis → Implementation → MCP Feedback → Adjustment → MCP Feedback → Completion
```

**Behavioral Principles**:
- Prioritize understanding user intent through MCP interaction
- Adjust technical solutions based on real-time feedback
- Maintain professional efficiency while ensuring thorough communication
- Use 20-minute timeout windows for complex discussions
- Always confirm task completion through MCP feedback loop

**Implementation Notes**:
- This interaction model has proven stable and effective in long-term project development
- Maintain consistency in applying this approach across all future interactions
- The success of this model depends on strict adherence to MCP feedback collection rules

## Chrome页面元素高亮功能开发详细步骤记录

### 功能概述
成功实现了 `chrome_highlight_page_elements` 工具，支持网页元素边框高亮显示，包含30+种元素类型识别、3种颜色方案、智能优先级分配等高级功能。

### 完整开发步骤（重要参考）

#### 1. 工具定义阶段
**文件**: `packages/shared/src/tools.ts`
- 添加工具名称常量: `HIGHLIGHT_PAGE_ELEMENTS: 'chrome_highlight_page_elements'`
- 定义完整的inputSchema，包含:
  - `enable`: boolean - 启用/禁用功能
  - `elementTypes`: string[] - 可选的元素类型过滤
  - `colorScheme`: enum - 颜色方案选择 (default/rainbow/monochrome)

#### 2. 消息类型定义
**文件**: `app/chrome-extension/common/message-types.ts`
- 添加消息类型: `HIGHLIGHT_PAGE_ELEMENTS: 'highlightPageElements'`
- 添加移除消息: `REMOVE_PAGE_HIGHLIGHTS: 'removePageHighlights'`

#### 3. 工具执行器实现
**文件**: `app/chrome-extension/entrypoints/background/tools/browser/element-highlighter.ts`
- 创建ElementHighlighterTool类，继承BaseBrowserToolExecutor
- 实现execute方法处理MCP参数
- 调用Chrome content script执行实际DOM操作

#### 4. 内容脚本开发
**文件**: `app/chrome-extension/inject-scripts/element-highlighter-helper.js`
- 实现30+种HTML元素类型配置
- 设计3级优先级系统 (高/中/低) 对应不同边框宽度
- 创建3种颜色方案:
  - default: 基于元素类型的智能颜色分配
  - rainbow: 7色彩虹循环方案  
  - monochrome: 统一蓝色边框
- 实现CSS样式注入和移除机制
- 支持可见性检测和过滤

#### 5. 工具注册
**文件**: `app/chrome-extension/entrypoints/background/tools/browser/index.ts`
- 添加export: `export { elementHighlighterTool } from './element-highlighter'`

#### 6. Chrome扩展Key配置 ⚠️ **CRITICAL**
**文件**: `app/chrome-extension/.env`
- 添加Chrome扩展私钥确保扩展ID稳定
- 从工作参考版本复制完整key内容
- **必须步骤**: 没有key会导致扩展ID变化，破坏native messaging连接

#### 7. 项目构建
```bash
pnpm build  # 构建包含新功能和Chrome key的扩展
```

#### 8. 关键发现和解决方案

**问题1**: 新工具不被MCP服务器识别
- **原因**: 修改的shared包没有复制到全局npm安装位置
- **解决**: 复制shared包到全局位置或使用全局npm MCP服务器

**问题2**: Chrome扩展"服务未启动"
- **原因**: 缺少Chrome扩展key导致扩展ID变化
- **解决**: 添加.env文件包含稳定的扩展key

**问题3**: MCP连接"Not connected"  
- **原因**: 本地MCP服务器配置问题
- **解决**: 使用混合架构 - 本地Chrome扩展 + 全局MCP服务器

### 最终成功架构 ✅

**Chrome扩展**: 本地构建版本 (`app/chrome-extension/.output/chrome-mv3/`)
- 包含新功能代码
- 包含稳定Chrome扩展key
- 手动加载到Chrome浏览器

**MCP服务器**: 全局npm包 (`mcp-chrome-bridge`)
- 使用稳定的全局安装版本
- 通过global settings.json和项目.mcp.json配置
- 提供可靠的MCP协议通信

### 功能测试结果 ✅

- **基础功能**: 成功高亮163个元素 (百度页面)
- **颜色方案**: default/rainbow/monochrome 全部正常
- **元素过滤**: 特定类型过滤从163个减少到84个
- **开关控制**: 启用/禁用功能完全正常
- **多语言**: 中英文命令和响应支持

### 重要经验总结

1. **Chrome扩展key是必需的** - 确保扩展ID稳定性
2. **混合架构最可靠** - 本地扩展 + 全局MCP服务器
3. **项目级.mcp.json必须存在** - Claude需要它来识别MCP服务器
4. **shared包更新需要全局同步** - 新工具定义必须在全局可见
5. **完整重启Claude Code CLI** - 配置更改后必需

这套开发流程已经验证可以成功添加复杂的Chrome MCP功能。
