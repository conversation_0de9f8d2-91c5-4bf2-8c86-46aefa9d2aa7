   │ 页面元素高亮显示功能实现计划                                                                                              │
     │                                                                                                                           │
     │ 🎯 功能目标                                                                                                               │
     │                                                                                                                           │
     │ 在MCP客户端中通过命令 chrome_highlight_page_elements 或中文                                                               │
     │ 显示页面所有元素，为当前网页的所有HTML元素添加彩色边框高亮显示。                                                          │
     │                                                                                                                           │
     │ 📝 需要修改/新建的文件（共5个）                                                                                           │
     │                                                                                                                           │
     │ 1. 工具Schema定义                                                                                                         │
     │                                                                                                                           │
     │ 文件：packages/shared/src/tools.ts                                                                                        │
     │ 操作：添加新工具定义                                                                                                      │
     │ - 在 TOOL_NAMES.BROWSER 中添加 HIGHLIGHT_PAGE_ELEMENTS                                                                    │
     │ - 在 TOOL_SCHEMAS 数组中添加工具schema，支持参数：                                                                        │
     │   - enable: boolean - 启用/禁用高亮                                                                                       │
     │   - elementTypes: string[] - 指定要高亮的元素类型                                                                         │
     │   - colorScheme: string - 颜色方案（default/rainbow/monochrome）                                                          │
     │                                                                                                                           │
     │ 2. 消息类型定义                                                                                                           │
     │                                                                                                                           │
     │ 文件：app/chrome-extension/common/message-types.ts操作：添加消息类型常量                                                  │
     │ - HIGHLIGHT_PAGE_ELEMENTS: 'highlightPageElements'                                                                        │
     │ - REMOVE_PAGE_HIGHLIGHTS: 'removePageHighlights'                                                                          │
     │                                                                                                                           │
     │ 3. 工具执行器实现                                                                                                         │
     │                                                                                                                           │
     │ 文件：app/chrome-extension/entrypoints/background/tools/browser/element-highlighter.ts（新建）                            │
     │ 操作：创建工具执行器类                                                                                                    │
     │ - 继承 BaseBrowserToolExecutor                                                                                            │
     │ - 处理MCP参数和错误处理                                                                                                   │
     │ - 注入内容脚本并与之通信                                                                                                  │
     │ - 返回操作结果                                                                                                            │
     │                                                                                                                           │
     │ 4. 内容脚本实现                                                                                                           │
     │                                                                                                                           │
     │ 文件：app/chrome-extension/inject-scripts/element-highlighter-helper.js（新建）                                           │
     │ 操作：创建页面元素高亮脚本                                                                                                │
     │ - 实现20+种元素类型识别（div、span、button、input等）                                                                     │
     │ - 设计智能颜色分配系统（每种元素不同颜色）                                                                                │
     │ - 动态CSS样式注入和清理                                                                                                   │
     │ - Chrome消息监听器                                                                                                        │
     │                                                                                                                           │
     │ 5. 工具导出配置                                                                                                           │
     │                                                                                                                           │
     │ 文件：app/chrome-extension/entrypoints/background/tools/browser/index.ts                                                  │
     │ 操作：导出新工具                                                                                                          │
     │ - 添加 export { elementHighlighterTool } from './element-highlighter'                                                     │
     │                                                                                                                           │
     │ 🔧 核心技术实现                                                                                                           │
     │                                                                                                                           │
     │ 元素识别系统                                                                                                              │
     │                                                                                                                           │
     │ const ELEMENT_HIGHLIGHT_CONFIG = {                                                                                        │
     │   button: { color: '#FF6B6B', priority: 10, borderWidth: '2px' },                                                         │
     │   input: { color: '#4ECDC4', priority: 9, borderWidth: '2px' },                                                           │
     │   a: { color: '#45B7D1', priority: 8, borderWidth: '2px' },                                                               │
     │   div: { color: '#96CEB4', priority: 3, borderWidth: '1px' },                                                             │
     │   span: { color: '#FECA57', priority: 2, borderWidth: '1px' },                                                            │
     │   // ... 更多元素类型                                                                                                     │
     │ }                                                                                                                         │
     │                                                                                                                           │
     │ CSS样式注入策略                                                                                                           │
     │                                                                                                                           │
     │ - 创建唯一的样式表ID：__chrome_mcp_element_highlighter__                                                                  │
     │ - 使用 !important 确保样式优先级                                                                                          │
     │ - 批处理DOM操作优化性能                                                                                                   │
     │ - 提供一键清理高亮功能                                                                                                    │
     │                                                                                                                           │
     │ 🧪 测试和验证流程                                                                                                          │
     │                                                                                                                           │
     │ 开发流程                                                                                                                  │
     │                                                                                                                           │
     │ 1. 修改代码 → 编辑上述5个文件                                                                                             │
     │ 2. 构建项目 → pnpm build（生成 dist/ 目录）                                                                               │
     │ 3. 加载扩展 → Chrome中加载 app/chrome-extension/dist/ 文件夹                                                              │
     │ 4. 测试功能 → 在MCP客户端中测试命令                                                                                       │
     │                                                                                                                           │
     │ 验证命令                                                                                                                  │
     │                                                                                                                           │
     │ # 基础命令                                                                                                                │
     │ chrome_highlight_page_elements                                                                                            │
     │                                                                                                                           │
     │ # 带参数                                                                                                                  │
     │ chrome_highlight_page_elements {"enable": true, "colorScheme": "rainbow"}                                                 │
     │                                                                                                                           │
     │ # 中文命令                                                                                                                │
     │ 显示页面所有元素                                                                                                          │
     │                                                                                                                           │
     │ ⚡ 性能优化                                                                                                                │
     │                                                                                                                           │
     │ - 过滤不可见元素（display:none等）                                                                                        │
     │ - 按元素优先级排序                                                                                                        │
     │ - 使用 requestAnimationFrame 优化重绘                                                                                     │
     │ - 元素数量限制机制                                                                                                        │
     │                                                                                                                           │
     │ 🎨 用户体验                                                                                                               │
     │                                                                                                                           │
     │ - 支持中英文命令触发                                                                                                      │
     │ - 不同元素类型使用不同颜色边框                                                                                            │
     │ - 一键开启/关闭功能                                                                                                       │
     │ - 不影响原有页面功能                                                                                                      │
     │                                                                                                                           │
     │ 这个计划涵盖了完整的功能实现，从MCP接口定义到Chrome扩展执行，确保功能完整性和代码质量。//
