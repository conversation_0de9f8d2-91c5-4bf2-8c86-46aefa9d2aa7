<!DOCTYPE html>
<html>
<head>
    <title>页面元素高亮控制器</title>
    <style>
        body { font-family: Arial; padding: 20px; background: #f5f5f5; }
        .control-panel {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 400px;
        }
        .switch {
            position: relative;
            display: inline-block;
            width: 60px;
            height: 34px;
        }
        .switch input { opacity: 0; width: 0; height: 0; }
        .slider {
            position: absolute;
            cursor: pointer;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: #ccc;
            transition: .4s;
            border-radius: 34px;
        }
        .slider:before {
            position: absolute;
            content: "";
            height: 26px; width: 26px;
            left: 4px; bottom: 4px;
            background-color: white;
            transition: .4s;
            border-radius: 50%;
        }
        input:checked + .slider { background-color: #2196F3; }
        input:checked + .slider:before { transform: translateX(26px); }
        .status { margin: 10px 0; font-weight: bold; }
        .controls { margin: 10px 0; }
        select, button { padding: 8px; margin: 5px; border-radius: 4px; border: 1px solid #ddd; }
        button { background: #2196F3; color: white; cursor: pointer; }
        button:hover { background: #1976D2; }
    </style>
</head>
<body>
    <div class="control-panel">
        <h2>🎨 页面元素高亮控制器</h2>

        <div class="controls">
            <label>
                <span>启用高亮：</span>
                <label class="switch">
                    <input type="checkbox" id="highlightToggle">
                    <span class="slider"></span>
                </label>
            </label>
        </div>

        <div class="controls">
            <label>颜色方案：</label>
            <select id="colorScheme">
                <option value="default">默认多彩</option>
                <option value="rainbow">彩虹色</option>
                <option value="monochrome">单色</option>
            </select>
        </div>

        <div class="controls">
            <label>元素类型：</label>
            <select id="elementType">
                <option value="all">所有元素</option>
                <option value="button">按钮</option>
                <option value="input">输入框</option>
                <option value="a">链接</option>
                <option value="div">DIV容器</option>
            </select>
        </div>

        <div class="status" id="status">状态：未连接</div>

        <button onclick="testConnection()">🔗 测试连接</button>
        <button onclick="getCurrentTab()">📄 获取当前页面</button>
    </div>

    <script>
        const API_BASE = 'http://localhost:9222';
        let currentTabId = null;

        // 开关切换事件
        document.getElementById('highlightToggle').addEventListener('change', function() {
            if (this.checked) {
                enableHighlight();
            } else {
                disableHighlight();
            }
        });

        // 颜色方案改变事件
        document.getElementById('colorScheme').addEventListener('change', function() {
            if (document.getElementById('highlightToggle').checked) {
                enableHighlight();
            }
        });

        // 元素类型改变事件
        document.getElementById('elementType').addEventListener('change', function() {
            if (document.getElementById('highlightToggle').checked) {
                enableHighlight();
            }
        });

        // 获取当前标签页
        async function getCurrentTab() {
            try {
                const response = await fetch(`${API_BASE}/json/tabs`);
                const tabs = await response.json();
                const activeTab = tabs.find(tab => tab.url && !tab.url.startsWith('chrome://'));

                if (activeTab) {
                    currentTabId = activeTab.id;
                    updateStatus(`✅ 已连接到: ${activeTab.title}`);
                    return activeTab;
                } else {
                    updateStatus('❌ 未找到有效标签页');
                    return null;
                }
            } catch (error) {
                updateStatus('❌ 连接失败，请确保Chrome已启动调试模式');
                return null;
            }
        }

        // 测试连接
        async function testConnection() {
            updateStatus('🔄 正在测试连接...');
            await getCurrentTab();
        }

        // 启用高亮
        async function enableHighlight() {
            if (!currentTabId) {
                await getCurrentTab();
                if (!currentTabId) return;
            }

            const colorScheme = document.getElementById('colorScheme').value;
            const elementType = document.getElementById('elementType').value;

            const script = generateHighlightScript(colorScheme, elementType);

            try {
                const response = await fetch(`${API_BASE}/json/runtime/evaluate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        expression: script
                    })
                });

                if (response.ok) {
                    updateStatus('✅ 高亮已启用');
                } else {
                    updateStatus('❌ 启用失败');
                }
            } catch (error) {
                updateStatus('❌ 执行失败');
            }
        }

        // 禁用高亮
        async function disableHighlight() {
            if (!currentTabId) return;

            const script = `
                document.querySelectorAll('[data-highlight-enabled]').forEach(el => {
                    el.style.border = el.getAttribute('data-original-border') || '';
                    el.removeAttribute('data-highlight-enabled');
                    el.removeAttribute('data-original-border');
                });
                console.log('高亮已禁用');
            `;

            try {
                await fetch(`${API_BASE}/json/runtime/evaluate`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ expression: script })
                });
                updateStatus('🔴 高亮已禁用');
            } catch (error) {
                updateStatus('❌ 禁用失败');
            }
        }

        // 生成高亮脚本
        function generateHighlightScript(colorScheme, elementType) {
            const colors = {
                default: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#F39C12'],
                rainbow: ['#FF0000', '#FF7F00', '#FFFF00', '#00FF00', '#0000FF', '#4B0082', '#9400D3'],
                monochrome: ['#2196F3']
            };

            const selector = elementType === 'all' ? '*' : elementType;
            const colorArray = colors[colorScheme] || colors.default;

            return `
                (function() {
                    // 先清除之前的高亮
                    document.querySelectorAll('[data-highlight-enabled]').forEach(el => {
                        el.style.border = el.getAttribute('data-original-border') || '';
                        el.removeAttribute('data-highlight-enabled');
                        el.removeAttribute('data-original-border');
                    });

                    // 应用新的高亮
                    const elements = document.querySelectorAll('${selector}');
                    const colors = ${JSON.stringify(colorArray)};
                    let count = 0;

                    elements.forEach((el, index) => {
                        if (el.offsetParent !== null && el.tagName !== 'HTML' && el.tagName !== 'BODY') {
                            const originalBorder = el.style.border;
                            el.setAttribute('data-original-border', originalBorder);
                            el.style.border = '2px solid ' + colors[index % colors.length];
                            el.style.boxSizing = 'border-box';
                            el.setAttribute('data-highlight-enabled', 'true');
                            count++;
                        }
                    });

                    console.log('已高亮 ' + count + ' 个元素，颜色方案: ${colorScheme}');
                    return count;
                })();
            `;
        }

        // 更新状态
        function updateStatus(message) {
            document.getElementById('status').textContent = '状态：' + message;
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
